{% for mp in part.marking_points %}
{% set mp_index = loop.index0 %}
<div class="marking-point-item p-2 border border-gray-200 rounded mb-2
    {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-score')|list %}
    border-red-200 bg-red-50
    {% endif %}
    {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-description')|list %}
    border-yellow-200 bg-yellow-50
    {% endif %}">
    <div class="flex items-center justify-between mb-2">
        <span class="marking-point-badge">Point {{ loop.index }}</span>
        <button type="button" class="text-red-600 hover:text-red-800 remove-marking-point-btn"
            onclick="removeMarkingPoint(this)">
            <i class="fas fa-times"></i> Remove
        </button>
    </div>
    <div class="flex flex-col space-y-2">
        <div>
            <label class="block text-xs font-medium text-gray-500">Description</label>
            <div class="mt-1">
                <textarea name="marking-points-{{ part_index }}-{{ mp_index }}-description"
                    class="latex-content block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm
                    {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-description')|list %}
                    border-yellow-500 bg-yellow-50
                    {% endif %}"
                    rows="2">{{ mp.description }}</textarea>

                <div class="mt-2 p-2 bg-gray-50 rounded latex-preview" id="latex-preview-{{ part_index }}-{{ mp_index }}">
                    {{ mp.description }}
                </div>

                {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-description')|list %}
                <p class="mt-1 text-xs text-yellow-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    {{ warnings[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-description')|map(attribute='message')|first }}
                </p>
                {% endif %}
            </div>
        </div>
        <div>
            <label class="block text-xs font-medium text-gray-500">Score</label>
            <input type="number" name="marking-points-{{ part_index }}-{{ mp_index }}-score" value="{{ mp.score }}"
                placeholder="score" step="0.1" min="0"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm
                {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-score')|list %}
                border-red-500 bg-red-50
                {% endif %}">

            {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-score')|list %}
            <p class="mt-1 text-xs text-red-600">
                <i class="fas fa-exclamation-circle mr-1"></i>
                {{ errors[cur_idx]|selectattr('id', 'equalto', 'marking-points-' ~ part_index ~ '-' ~ mp_index ~ '-score')|map(attribute='message')|first }}
            </p>
            {% endif %}
        </div>
    </div>
</div>
{% endfor %}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Render LaTeX in all preview divs
        document.querySelectorAll('.latex-preview').forEach(element => {
            renderMathInElement(element, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html'
            });
        });

        // Add event listeners to update preview when textarea changes (only actual textarea elements)
        document.querySelectorAll('textarea.latex-content').forEach(textarea => {
            const partIndex = textarea.name.split('-')[2];
            const mpIndex = textarea.name.split('-')[3];
            const previewDiv = document.getElementById(`latex-preview-${partIndex}-${mpIndex}`);

            if (previewDiv) {
                textarea.addEventListener('input', function() {
                    previewDiv.textContent = this.value;
                    renderMathInElement(previewDiv, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true}
                        ],
                        throwOnError: false,
                        output: 'html'
                    });
                });
            }
        });
    });
</script>