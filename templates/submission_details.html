{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header with animated gradient -->
    <div class="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl overflow-hidden mb-8 shadow-lg transform transition-all duration-300 hover:shadow-xl">
        <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
        <div class="relative p-8">
            <div class="flex flex-col items-center">
                <h1 class="text-3xl font-bold text-white tracking-tight">Submission Details</h1>
                <div class="mt-2 flex items-center space-x-2">
                    <span class="inline-flex items-center rounded-full bg-white/20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        {{ submission.timestamp.strftime('%b %d, %Y at %H:%M') }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-8 space-y-6">
            <!-- Question Information Card -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-question"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Question Information</h2>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex flex-col space-y-2">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-book-open mr-2 text-indigo-500"></i>
                                <span>Question</span>
                            </div>
                            <a href="{{ url_for('load_question', question_id=submission.question_id) }}" 
                               class="text-base text-gray-900 hover:text-indigo-600 transition-colors duration-200 group">
                                {{ submission.question.description }}
                                <i class="fas fa-external-link-alt ml-1.5 text-xs opacity-70 transition-transform duration-200 group-hover:translate-x-0.5"></i>
                            </a>
                        </div>
                        
                        <div class="flex flex-col space-y-2">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-puzzle-piece mr-2 text-indigo-500"></i>
                                <span>Part</span>
                            </div>
                            <p class="text-base text-gray-900">{{ submission.part.description }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Answer Comparison Card -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-check-double"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Answer Comparison</h2>
                    </div>
                    
                    <div class="space-y-6">
                        <!-- User's Answer -->
                        <div class="relative">
                            <div class="flex items-center mb-3">
                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-700 mr-3">
                                    <i class="fas fa-user"></i>
                                </div>
                                <h3 class="text-base font-medium text-gray-900">{{ submission.user.username }}'s Answer</h3>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-5 border-l-4 border-indigo-400">
                                <p class="text-gray-700 whitespace-pre-wrap">{{ submission.answer }}</p>
                            </div>
                        </div>
                        
                        <!-- Correct Answer -->
                        {% if submission.part.answer %}
                        <div class="relative">
                            <div class="flex items-center mb-3">
                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-700 mr-3">
                                    <i class="fas fa-check"></i>
                                </div>
                                <h3 class="text-base font-medium text-gray-900">Correct Answer</h3>
                            </div>
                            <div class="bg-green-50 rounded-lg p-5 border-l-4 border-green-400">
                                <p class="text-green-800 whitespace-pre-wrap">{{ submission.part.answer }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Marking Points Evaluation Card -->
            {% if submission.part.marking_points %}
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Marking Scheme & Evaluation</h2>
                    </div>

                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Marking Point</th>
                                    <th scope="col" class="px-3 py-3.5 text-center text-sm font-semibold text-gray-900">Status</th>
                                    <th scope="col" class="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">Score</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                {% set feedback_data = {} %}
                                {% if submission.feedback %}
                                    {% set feedback_data = submission.feedback|from_json %}
                                {% endif %}

                                {% for mp in submission.part.marking_points|sort(attribute='order') %}
                                {% set evaluation = feedback_data.evaluated_points|selectattr('id', 'equalto', mp.id)|first if feedback_data.evaluated_points else None %}

                                <tr class="{% if evaluation and evaluation.achieved %}bg-green-50{% elif evaluation and evaluation.partial %}bg-yellow-50{% elif evaluation %}bg-red-50{% endif %}">
                                    <td class="py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-6">
                                        <div class="font-medium">{{ mp.description }}</div>
                                        {% if evaluation and evaluation.evidence %}
                                        <div class="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded border-l-4 {% if evaluation.achieved %}border-green-400{% elif evaluation.partial %}border-yellow-400{% else %}border-red-400{% endif %}">
                                            <strong>Evidence found:</strong> "{{ evaluation.evidence }}"
                                        </div>
                                        {% elif evaluation and not evaluation.achieved and not evaluation.partial %}
                                        <div class="mt-2 text-xs text-red-600 italic">
                                            No evidence found in your answer for this marking point.
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="px-3 py-4 text-center text-sm">
                                        {% if evaluation %}
                                            {% if evaluation.achieved %}
                                                <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                                    <i class="fas fa-check mr-1"></i>
                                                    Achieved
                                                </span>
                                            {% elif evaluation.partial %}
                                                <span class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                                                    <i class="fas fa-minus mr-1"></i>
                                                    Partial
                                                </span>
                                            {% else %}
                                                <span class="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                                    <i class="fas fa-times mr-1"></i>
                                                    Not Achieved
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-600">
                                                <i class="fas fa-question mr-1"></i>
                                                Not Evaluated
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-3 py-4 text-right text-sm">
                                        {% if evaluation %}
                                            <div class="font-medium {% if evaluation.achieved %}text-green-600{% elif evaluation.partial %}text-yellow-600{% else %}text-red-600{% endif %}">
                                                {{ "%.1f"|format(evaluation.achieved_score) }}/{{ "%.1f"|format(mp.score) }}
                                            </div>
                                        {% else %}
                                            <div class="text-gray-500">{{ mp.score }}</div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-4 space-y-6">
            <!-- User Information Card -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-user"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">User Information</h2>
                    </div>
                    
                    <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white text-xl font-bold">
                                {{ submission.user.username[:1].upper() }}
                            </div>
                        </div>
                        <div>
                            <p class="text-base font-medium text-gray-900">{{ submission.user.username }}</p>
                            <p class="text-sm text-gray-500">Student</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Score Card -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-star"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Score</h2>
                    </div>
                    
                    <div class="flex flex-col items-center p-6 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg">
                        <div class="relative w-32 h-32 mb-4">
                            <svg class="w-full h-full" viewBox="0 0 36 36">
                                <path class="stroke-current text-gray-200" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                <path class="stroke-current {% if submission.score == submission.part.score %}text-green-500{% elif submission.score > 0 %}text-amber-500{% else %}text-red-500{% endif %}" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="{{ (submission.score / submission.part.score) * 100 }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                <text x="18" y="20.5" class="fill-current {% if submission.score == submission.part.score %}text-green-700{% elif submission.score > 0 %}text-amber-700{% else %}text-red-700{% endif %} font-bold text-5xl" text-anchor="middle">{{ (submission.score / submission.part.score) * 100 | int }}%</text>
                            </svg>
                        </div>
                        
                        <div class="text-center">
                            <p class="text-2xl font-bold {% if submission.score == submission.part.score %}text-green-600{% elif submission.score > 0 %}text-amber-600{% else %}text-red-600{% endif %}">
                                {{ submission.score }} / {{ submission.part.score }}
                            </p>
                            <p class="mt-1 text-sm text-gray-500">Points earned</p>
                        </div>
                        
                        <div class="mt-4 w-full">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-xs text-gray-500">Confidence Level</span>
                                <span class="text-xs font-medium {% if submission.confidence_level == 'High' %}text-green-600{% elif submission.confidence_level == 'Medium' %}text-amber-600{% else %}text-blue-600{% endif %}">
                                    {{ submission.confidence_level }}
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="{% if submission.confidence_level == 'High' %}bg-green-500{% elif submission.confidence_level == 'Medium' %}bg-amber-500{% else %}bg-blue-500{% endif %} h-1.5 rounded-full" style="width: {% if submission.confidence_level == 'High' %}100%{% elif submission.confidence_level == 'Medium' %}66%{% else %}33%{% endif %}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions Card -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Actions</h2>
                    </div>
                    
                    <div class="space-y-3">
                        <a href="{{ url_for('load_question', question_id=submission.question_id) }}" 
                           class="flex items-center justify-between w-full p-3 bg-indigo-50 rounded-lg text-indigo-700 hover:bg-indigo-100 transition-colors duration-200">
                            <span class="flex items-center">
                                <i class="fas fa-redo-alt mr-2"></i>
                                Try Again
                            </span>
                            <i class="fas fa-chevron-right text-xs opacity-70"></i>
                        </a>
                        
                        <a href="{{ url_for('submissions') }}" 
                           class="flex items-center justify-between w-full p-3 bg-gray-50 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                            <span class="flex items-center">
                                <i class="fas fa-history mr-2"></i>
                                All Submissions
                            </span>
                            <i class="fas fa-chevron-right text-xs opacity-70"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Animated background grid */
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    }
    
    /* Subtle animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .rounded-xl {
        animation: fadeIn 0.5s ease-out forwards;
        animation-delay: calc(var(--animation-order, 0) * 0.1s);
        opacity: 0;
    }
    
    /* Circular progress animation */
    @keyframes progress {
        0% {
            stroke-dasharray: 0 100;
        }
    }
    
    .stroke-current {
        animation: progress 1s ease-out forwards;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation order to cards
        const cards = document.querySelectorAll('.rounded-xl');
        cards.forEach((card, index) => {
            card.style.setProperty('--animation-order', index);
        });
        
        // Add hover effect to comparison
        const userAnswer = document.querySelector('.border-indigo-400').parentElement;
        const correctAnswer = document.querySelector('.border-green-400').parentElement;
        
        userAnswer.addEventListener('mouseenter', function() {
            correctAnswer.classList.add('opacity-50');
            correctAnswer.classList.add('transition-opacity');
            correctAnswer.classList.add('duration-200');
        });
        
        userAnswer.addEventListener('mouseleave', function() {
            correctAnswer.classList.remove('opacity-50');
        });
        
        correctAnswer.addEventListener('mouseenter', function() {
            userAnswer.classList.add('opacity-50');
            userAnswer.classList.add('transition-opacity');
            userAnswer.classList.add('duration-200');
        });
        
        correctAnswer.addEventListener('mouseleave', function() {
            userAnswer.classList.remove('opacity-50');
        });
    });
</script>
{% endblock %}
