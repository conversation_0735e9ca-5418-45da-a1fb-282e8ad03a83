import os
import json
from flask import Blueprint, request, jsonify
from groq import Groq
from google import genai
from dotenv import load_dotenv
from json_repair import repair_json
import json
from pydantic import BaseModel, ValidationError
from models import Question, db
load_dotenv() # Load environment variables from .env

ai_bp = Blueprint("ai_helpers_test", __name__)

# TODO: build failsafe, have some global flag that disables all genai functions if there is any errors
# Probably intitialise during factory init
api_key = os.environ.get('GEMINI_API_KEY')
gemini_client = None
if not api_key:
    print("Warning: API key for gemini not in environmental variables")
else:
    gemini_client = genai.Client(api_key=api_key)


class Point(BaseModel):
    description: str
    score: int

def validate_point(point):
    if not isinstance(point.description, str):
        raise ValueError(f"Marking point missing description. From: {point}")
    
    if not isinstance(point.score, int):
        raise ValueError(f"Marking point score must be an integer. From {point}")
    
    if len(point.description) == 0:
        raise ValueError(f"Marking point missing text. From: {point}")
    
    if point.score <= 0:
        raise ValueError(f"Marking point must have a positive score. From: {point}")
    
    return True

def validate_score(total_score, score):
    try:
        assert(total_score == score)
    except:
        print(f"Sum score of parts ({total_score}) does not equal total score of question ({score})")
        raise AssertionError

def generate_marking_points(description, answer, score, client=gemini_client):
    prompt = '''
You are an expert assistant tasked with creating detailed marking points for educational questions.
Given the question part description, the suggested answer, and the total score allocated, generate a list of specific marking points. 
1. The UNION of the marking points should cover the entire answer. Do not rephrase the answer in your own words, instead, directly copy from the model answer.
2. The total score of all marking points should equal the total score provided.
3. DO NOT WRITE DESCRIPTIVE TERMS LIKE "CORRECT XXX", WRITE THE ANSWER YOU WANT DIRECTLY.
4. EACH MARKING POINT MUST BE AN INTEGER NUMBER OF MARKS. IF TOO MANY POINTS AND NOT ENOUGH MARKS, COMBINE POINTS INTO ONE POINT. 
5. Hence, you are essentially tasked to split the answer into logical parts, where number of parts is equal to the total score of that question
6. Each marking point should have a description and an associated score. The sum of the scores for all marking points should ideally equal the total score provided, but prioritize logical breakdown over exact score matching if necessary.
7. Ensure the output is ONLY a valid JSON list of objects, where each object has a 'description' (string) and a 'score' (number) key. Do not include any introductory text, explanations, or markdown formatting outside the JSON structure.
8. DO NOT PREFIX RESPONSE WITH ```json OR ANY OTHER TEXT. 
9. For latex symbols and commands, you will only need 2 backslashas one will be escaped by python. 
Question Part Description:
{description}

Suggested Answer:
{answer}

Total Score for this Part: {score}
'''.format(description=description, answer=answer, score=score)
    
    response = client.models.generate_content(
        model='gemini-2.5-flash-preview-05-20',
        contents=prompt,
        config={
            'response_mime_type': 'application/json',
            'response_schema': list[Point]
        },
    )

    outs = []
    total_score = 0    
    for point in response.parsed:
        if validate_point(point):
            outs.append(point.model_dump())
            total_score += point.score

    validate_score(total_score, score)

    return outs

def generate_question_title(question_description, parts_descriptions, client=gemini_client):
    """
    Generate a concise, descriptive title for a question based on its description and parts.
    """
    if not client:
        raise ValueError("Gemini client not available")

    # Combine question description and parts for context
    full_context = f"Question: {question_description}\n\n"
    if parts_descriptions:
        full_context += "Parts:\n"
        for i, part_desc in enumerate(parts_descriptions, 1):
            full_context += f"{i}. {part_desc}\n"

    prompt = f'''
You are an expert assistant tasked with creating concise, descriptive titles for educational questions.

Given the question description and its parts, generate a short title (maximum 8 words) that captures the main concept or topic being tested. Note that the Subject is not ambiguous.

Guidelines:
1. Keep it under 8 words
2. Focus on the main concept, topic, or skill being tested
3. Use clear, academic language
4. Avoid generic words like "Question about" or "Problem on"
5. Include key scientific terms or concepts when relevant
6. Make it specific enough to distinguish from other questions

Question Content:
{full_context}

Generate only the title text, no additional formatting or explanation.
'''

    response = client.models.generate_content(
        model='gemini-2.5-flash-preview-05-20',
        contents=prompt
    )

    # Extract and clean the title
    title = response.text.strip()

    # Remove any quotes or extra formatting
    title = title.strip('"\'')

    # Ensure it's not too long (fallback safety)
    if len(title) > 100:
        title = title[:97] + "..."

    return title

@ai_bp.route("/generate_question_title", methods=["POST"])
def generate_question_title_route():
    """API endpoint to generate a title for a question"""
    data = request.get_json()
    if not data:
        return jsonify({"message": "Invalid request body"}), 400

    question_description = data.get('questionDescription', '')
    parts_descriptions = data.get('partsDescriptions', [])

    if not question_description and not parts_descriptions:
        return jsonify({"message": "Either questionDescription or partsDescriptions must be provided"}), 400

    try:
        title = generate_question_title(question_description, parts_descriptions, gemini_client)
        return jsonify({"title": title})
    except Exception as e:
        return jsonify({"message": f"Error generating title: {str(e)}"}), 500

@ai_bp.route("/generate_title_for_question/<int:question_id>", methods=["POST"])
def generate_title_for_question_route(question_id):
    """API endpoint to generate and update title for an existing question by ID"""
    try:
        question = Question.query.get_or_404(question_id)

        # Collect question description and parts descriptions
        question_description = question.description or ""
        parts_descriptions = []

        if question.parts:
            for part in question.parts:
                if part.description:
                    parts_descriptions.append(part.description)

        if not question_description and not parts_descriptions:
            return jsonify({"message": "Question has no description or parts to generate title from"}), 400

        # Generate the title
        title = generate_question_title(question_description, parts_descriptions, gemini_client)

        # Update the question title in database
        question.title = title
        db.session.commit()

        return jsonify({
            "title": title,
            "message": "Title generated and saved successfully"
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({"message": f"Error generating title: {str(e)}"}), 500

def generate_and_update_question_title(question_id, client=gemini_client):
    """
    Standalone function to generate and update a question title by ID.
    Returns the generated title or raises an exception.
    """
    if not client:
        raise ValueError("Gemini client not available")

    question = Question.query.get(question_id)
    if not question:
        raise ValueError(f"Question with ID {question_id} not found")

    # Collect question description and parts descriptions
    question_description = question.description or ""
    parts_descriptions = []

    if question.parts:
        for part in question.parts:
            if part.description:
                parts_descriptions.append(part.description)

    if not question_description and not parts_descriptions:
        raise ValueError("Question has no description or parts to generate title from")

    # Generate the title
    title = generate_question_title(question_description, parts_descriptions, client)

    # Update the question title in database
    question.title = title
    db.session.commit()

    return title

@ai_bp.route("/generate_marking_points", methods=["POST"])
def generate_marking_point_route():
    data = request.get_json()
    if not data:
        return jsonify({"message": "Invalid request body"}), 400

    part_description = data.get('partDescription')
    part_answer = data.get('partAnswer')
    part_score = data.get('partScore')

    if not all([part_description, part_answer, part_score is not None]):
        return jsonify({"message": "Missing required fields: partDescription, partAnswer, partScore"}), 400

    try:
        part_score = int(part_score) # Ensure score is an integer
    except ValueError:
        return jsonify({"message": "Invalid score format. Must be an integer."}), 400


    marking_points = generate_marking_points(part_description, part_answer, part_score, gemini_client)
    return jsonify(marking_points)    
# result = generate_marking_points('By considering structure and bonding, explain why MgF2 has a higher melting point than NaF.','Mg2+ is doubly-charged compared to Na+, hence the magnitude of lattice energy for MgF2 is greater so the ionic bond in MgF2‚ is stronger and requires more energy to overcome.', 2, client)
# print(result)
