/**
 * Robust Time Tracker - A more accurate time tracking system for multiple tabs
 * Uses modern browser APIs for better coordination and accuracy
 */

// UUID fix is now handled in base.html template

class RobustTimeTracker {
    constructor(options = {}) {
        this.userId = options.userId;
        this.tabId = this.generateTabId();
        this.isActive = false;
        this.startTime = null;
        this.totalTime = 0;
        this.lastSentTime = 0;
        this.idleTimeout = options.idleTimeout || 60000; // 60 seconds
        this.updateInterval = options.updateInterval || 15000; // 15 seconds
        this.heartbeatInterval = options.heartbeatInterval || 2000; // 2 seconds

        // State tracking
        this.isVisible = !document.hidden;
        this.isIdle = false;
        this.lastActivity = Date.now();

        // Timers
        this.updateTimer = null;
        this.heartbeatTimer = null;
        this.idleTimer = null;

        // Tab coordination
        this.broadcastChannel = null;
        this.activeTabId = null;
        this.lastHeartbeat = 0;

        // Callbacks
        this.onTimeUpdate = options.onTimeUpdate || (() => {});
        this.onActiveChange = options.onActiveChange || (() => {});

        this.init();
    }

    generateTabId() {
        return 'tab_' + Math.random().toString(36).substring(2, 15) + '_' + Date.now();
    }

    init() {
        console.log('RobustTimeTracker: Initializing tab', this.tabId);

        // Set up tab coordination
        this.setupTabCoordination();

        // Set up visibility tracking
        this.setupVisibilityTracking();

        // Set up idle detection
        this.setupIdleDetection();

        // Set up activity listeners
        this.setupActivityListeners();

        // Set up cleanup
        this.setupCleanup();

        // Start coordination
        this.startCoordination();
    }

    setupTabCoordination() {
        // Try to use BroadcastChannel for modern browsers
        if (typeof BroadcastChannel !== 'undefined') {
            try {
                this.broadcastChannel = new BroadcastChannel('time_tracker_' + this.userId);
                this.broadcastChannel.onmessage = (event) => {
                    this.handleBroadcastMessage(event.data);
                };
                console.log('RobustTimeTracker: Using BroadcastChannel for coordination');
            } catch (e) {
                console.warn('RobustTimeTracker: BroadcastChannel failed, falling back to localStorage');
                this.broadcastChannel = null;
            }
        }

        // Fallback to localStorage for older browsers
        if (!this.broadcastChannel) {
            window.addEventListener('storage', (event) => {
                if (event.key === 'time_tracker_coordination_' + this.userId) {
                    this.handleStorageMessage(event.newValue);
                }
            });
        }
    }

    setupVisibilityTracking() {
        // Use Page Visibility API
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            console.log('RobustTimeTracker: Visibility changed to', this.isVisible ? 'visible' : 'hidden');
            this.handleVisibilityChange();
        });

        // Additional focus/blur events for better coverage
        window.addEventListener('focus', () => {
            this.isVisible = true;
            this.handleVisibilityChange();
        });

        window.addEventListener('blur', () => {
            this.isVisible = false;
            this.handleVisibilityChange();
        });
    }

    setupIdleDetection() {
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

        activityEvents.forEach(event => {
            document.addEventListener(event, () => {
                this.recordActivity();
            }, true);
        });

        // Check for idle state periodically
        this.idleTimer = setInterval(() => {
            this.checkIdleState();
        }, 1000);
    }

    setupActivityListeners() {
        // Record initial activity
        this.recordActivity();
    }

    setupCleanup() {
        // Clean up when page unloads
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Clean up when page is hidden (for mobile browsers)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.sendFinalUpdate();
            }
        });
    }

    startCoordination() {
        // Start heartbeat to coordinate with other tabs
        this.heartbeatTimer = setInterval(() => {
            this.sendHeartbeat();
            this.checkActiveTab();
        }, this.heartbeatInterval);

        // Start update timer
        this.updateTimer = setInterval(() => {
            if (this.isActive) {
                this.sendTimeUpdate();
            }
        }, this.updateInterval);

        // Initial coordination check
        this.checkActiveTab();
    }

    handleBroadcastMessage(data) {
        if (data.type === 'heartbeat' && data.tabId !== this.tabId) {
            this.handleOtherTabHeartbeat(data);
        } else if (data.type === 'claim_active' && data.tabId !== this.tabId) {
            this.handleOtherTabClaim(data);
        }
    }

    handleStorageMessage(value) {
        if (!value) return;

        try {
            const data = JSON.parse(value);
            this.handleBroadcastMessage(data);
        } catch (e) {
            console.error('RobustTimeTracker: Error parsing storage message', e);
        }
    }

    sendMessage(data) {
        if (this.broadcastChannel) {
            this.broadcastChannel.postMessage(data);
        } else {
            // Fallback to localStorage
            const key = 'time_tracker_coordination_' + this.userId;
            localStorage.setItem(key, JSON.stringify(data));
            // Clear immediately to trigger storage event
            setTimeout(() => localStorage.removeItem(key), 100);
        }
    }

    sendHeartbeat() {
        if (this.isActive) {
            this.sendMessage({
                type: 'heartbeat',
                tabId: this.tabId,
                timestamp: Date.now(),
                isVisible: this.isVisible,
                isIdle: this.isIdle
            });
        }
    }

    handleOtherTabHeartbeat(data) {
        // If another tab is active and this one isn't, that's fine
        if (data.isVisible && !data.isIdle && this.activeTabId !== this.tabId) {
            this.activeTabId = data.tabId;
            this.lastHeartbeat = data.timestamp;
        }
    }

    handleOtherTabClaim(data) {
        // Another tab is claiming to be active
        if (data.isVisible && !data.isIdle) {
            if (this.isActive && (!this.isVisible || this.isIdle)) {
                // We should give up being active
                this.setActive(false);
                this.activeTabId = data.tabId;
            }
        }
    }

    checkActiveTab() {
        const now = Date.now();
        const shouldBeActive = this.isVisible && !this.isIdle;

        // If we should be active but aren't
        if (shouldBeActive && !this.isActive) {
            // Check if there's another active tab
            if (!this.activeTabId || (now - this.lastHeartbeat > 5000)) {
                // No other active tab or it's been too long, claim active status
                this.claimActive();
            }
        }

        // If we are active but shouldn't be
        if (this.isActive && !shouldBeActive) {
            this.setActive(false);
        }
    }

    claimActive() {
        this.sendMessage({
            type: 'claim_active',
            tabId: this.tabId,
            timestamp: Date.now(),
            isVisible: this.isVisible,
            isIdle: this.isIdle
        });

        this.setActive(true);
        this.activeTabId = this.tabId;
    }

    setActive(active) {
        if (this.isActive === active) return;

        const wasActive = this.isActive;
        this.isActive = active;

        console.log('RobustTimeTracker: Tab', this.tabId, active ? 'became active' : 'became inactive');

        if (active) {
            this.startTime = Date.now();
        } else if (wasActive && this.startTime) {
            // Add elapsed time to total
            const elapsed = Date.now() - this.startTime;
            this.totalTime += elapsed;
            this.startTime = null;
        }

        this.onActiveChange(active);
    }

    handleVisibilityChange() {
        this.recordActivity(); // Reset idle state when visibility changes
        this.checkActiveTab();
    }

    recordActivity() {
        this.lastActivity = Date.now();
        if (this.isIdle) {
            this.isIdle = false;
            console.log('RobustTimeTracker: User became active');
            this.checkActiveTab();
        }
    }

    checkIdleState() {
        const now = Date.now();
        const timeSinceActivity = now - this.lastActivity;

        if (!this.isIdle && timeSinceActivity > this.idleTimeout) {
            this.isIdle = true;
            console.log('RobustTimeTracker: User became idle');
            this.checkActiveTab();
        }
    }

    getCurrentTime() {
        let currentTotal = this.totalTime;

        if (this.isActive && this.startTime) {
            currentTotal += Date.now() - this.startTime;
        }

        return Math.round(currentTotal / 1000); // Return in seconds
    }

    sendTimeUpdate() {
        const currentTime = this.getCurrentTime();
        const timeToSend = currentTime - this.lastSentTime;

        if (timeToSend > 0) {
            console.log('RobustTimeTracker: Sending time update:', timeToSend, 'seconds');

            this.onTimeUpdate(timeToSend)
                .then(() => {
                    this.lastSentTime = currentTime;
                })
                .catch(error => {
                    console.error('RobustTimeTracker: Failed to send time update:', error);
                });
        }
    }

    sendFinalUpdate() {
        if (this.isActive) {
            this.sendTimeUpdate();
        }
    }

    cleanup() {
        console.log('RobustTimeTracker: Cleaning up tab', this.tabId);

        // Send final update
        this.sendFinalUpdate();

        // Clear timers
        if (this.updateTimer) clearInterval(this.updateTimer);
        if (this.heartbeatTimer) clearInterval(this.heartbeatTimer);
        if (this.idleTimer) clearInterval(this.idleTimer);

        // Close broadcast channel
        if (this.broadcastChannel) {
            this.broadcastChannel.close();
        }

        // Mark as inactive
        this.setActive(false);
    }

    // Public API methods for compatibility
    getTimeOnCurrentPageInSeconds() {
        return this.getCurrentTime();
    }

    // Static method to create and initialize tracker
    static initialize(options) {
        return new RobustTimeTracker(options);
    }
}

// Export for compatibility with existing code
window.RobustTimeTracker = RobustTimeTracker;
