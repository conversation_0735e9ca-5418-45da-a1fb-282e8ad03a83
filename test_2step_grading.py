#!/usr/bin/env python3
"""
Test script for the new 2-step grading system.
This script tests the new grade_step1 and grade_step2 endpoints.
"""

import requests
import json
import sys
import time

# Configuration
BASE_URL = "http://localhost:5000"
TEST_QUESTION_ID = 1  # Replace with actual question ID
TEST_PART_ID = 1      # Replace with actual part ID
TEST_ANSWER = "The answer is 42. This is because the question asks for the meaning of life."

# Test both routes
TEST_ROUTES = {
    'get_git_diff': f"{BASE_URL}/get_git_diff/{TEST_QUESTION_ID}/{TEST_PART_ID}",
    'check_answer': f"{BASE_URL}/check_answer/{TEST_QUESTION_ID}/{TEST_PART_ID}",
    'grade_step1': f"{BASE_URL}/grade_step1/{TEST_QUESTION_ID}/{TEST_PART_ID}",
    'grade_step2': f"{BASE_URL}/grade_step2/{TEST_QUESTION_ID}/{TEST_PART_ID}"
}

def test_login():
    """Test login to get session cookies"""
    print("Testing login...")
    
    # First get the login page to get any CSRF tokens if needed
    session = requests.Session()
    
    # Try to login (you'll need to replace with actual credentials)
    login_data = {
        'username': 'test_user',  # Replace with actual test user
        'password': 'test_pass'   # Replace with actual test password
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200:
        print("✓ Login successful")
        return session
    else:
        print(f"✗ Login failed: {response.status_code}")
        return None

def test_step1_grading(session):
    """Test the grade_step1 endpoint"""
    print("\nTesting Step 1 Grading...")
    
    # Prepare form data
    form_data = {
        'answer': TEST_ANSWER,
        'confidence_level': 'Medium'
    }
    
    try:
        response = session.post(
            f"{BASE_URL}/grade_step1/{TEST_QUESTION_ID}/{TEST_PART_ID}",
            data=form_data
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("✓ Step 1 grading successful")
                print(f"  Score: {data.get('score')}/{data.get('max_score')}")
                print(f"  Marking points: {len(data.get('marking_points', []))}")
                return data
            else:
                print(f"✗ Step 1 grading failed: {data.get('message')}")
                return None
        else:
            print(f"✗ Step 1 grading HTTP error: {response.status_code}")
            print(f"  Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Step 1 grading exception: {e}")
        return None

def test_step2_highlighting(session, step1_data):
    """Test the grade_step2 endpoint"""
    print("\nTesting Step 2 Highlighting...")
    
    # Prepare JSON data from step 1 results
    json_data = {
        'user_answer': step1_data.get('user_answer'),
        'evaluated_points': step1_data.get('marking_points'),
        'score': step1_data.get('score'),
        'timing': step1_data.get('timing', {})
    }
    
    try:
        response = session.post(
            f"{BASE_URL}/grade_step2/{TEST_QUESTION_ID}/{TEST_PART_ID}",
            json=json_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("✓ Step 2 highlighting successful")
                print(f"  Submission ID: {data.get('submission_id')}")
                print(f"  Answer length: {len(data.get('answer', ''))}")
                return data
            else:
                print(f"✗ Step 2 highlighting failed: {data.get('message')}")
                return None
        else:
            print(f"✗ Step 2 highlighting HTTP error: {response.status_code}")
            print(f"  Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Step 2 highlighting exception: {e}")
        return None

def test_backward_compatibility(session):
    """Test that the original check_answer endpoint still works"""
    print("\nTesting Backward Compatibility...")
    
    # Prepare form data
    form_data = {
        'answer': TEST_ANSWER,
        'confidence_level': 'Medium'
    }
    
    try:
        response = session.post(
            f"{BASE_URL}/check_answer/{TEST_QUESTION_ID}/{TEST_PART_ID}",
            data=form_data
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("✓ Backward compatibility successful")
                print(f"  Score: {data.get('score')}/{data.get('max_score')}")
                print(f"  Has marking points: {'marking_points' in data}")
                print(f"  Has highlighted answer: {'answer' in data}")
                return data
            else:
                print(f"✗ Backward compatibility failed: {data.get('message')}")
                return None
        else:
            print(f"✗ Backward compatibility HTTP error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ Backward compatibility exception: {e}")
        return None

def main():
    """Main test function"""
    print("=== 2-Step Grading System Test ===")
    print(f"Testing against: {BASE_URL}")
    print(f"Question ID: {TEST_QUESTION_ID}, Part ID: {TEST_PART_ID}")
    print(f"Test answer: {TEST_ANSWER[:50]}...")
    
    # Test login
    session = test_login()
    if not session:
        print("\n❌ Cannot proceed without login. Please check credentials.")
        return False
    
    # Test step 1
    step1_data = test_step1_grading(session)
    if not step1_data:
        print("\n❌ Step 1 failed. Cannot test step 2.")
        return False
    
    # Small delay between steps
    time.sleep(1)
    
    # Test step 2
    step2_data = test_step2_highlighting(session, step1_data)
    if not step2_data:
        print("\n❌ Step 2 failed.")
        return False
    
    # Test backward compatibility
    compat_data = test_backward_compatibility(session)
    if not compat_data:
        print("\n❌ Backward compatibility failed.")
        return False
    
    print("\n✅ All tests passed! 2-step grading system is working correctly.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
